<?php

use App\Http\Controllers\Api\AdvertisementController;
use App\Http\Controllers\Api\CartController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\CommonController;
use App\Http\Controllers\Api\HomeController;
use App\Http\Controllers\Api\CommunityController;
use App\Http\Controllers\Api\CouponController;
use App\Http\Controllers\Api\NotifyController;
use App\Http\Controllers\Api\O2oErrandOrderController;
use App\Http\Controllers\Api\PhoneProtectController;
use App\Http\Controllers\Api\UserCouponController;
use App\Http\Controllers\Api\CooperateController;
use App\Http\Controllers\Api\LocationController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\ShopController;
use App\Http\Controllers\Api\ShopSpuCatController;
use App\Http\Controllers\Api\ShopSpuController;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Api\VerificationCodesController;
use App\Http\Controllers\Api\UsersController;
use App\Http\Controllers\Api\ImagesController;
use App\Http\Controllers\Api\AddressController;
use App\Http\Controllers\Api\AuthorizationsController;
use App\Http\Controllers\Api\EasemobController;
use App\Http\Controllers\Api\RegionController;
use App\Http\Controllers\Api\UserAccountController;
use App\Http\Controllers\Api\RechargeOrderController;
use App\Http\Controllers\Api\PayController;
use App\Http\Controllers\Api\UserAccountFlowController;
use App\Http\Controllers\Api\SuggestionController;
use App\Http\Controllers\Api\HealthCertController;
use App\Http\Controllers\Api\TransportController;
use App\Http\Controllers\Api\RiderController;
use App\Http\Controllers\Api\ActivityController;
use App\Http\Controllers\Api\TaskController;
use App\Http\Controllers\Api\RiderApplyController;
use App\Http\Controllers\Api\RiderAccountController;
use App\Http\Controllers\Api\RiderAccountFlowController;
use App\Http\Controllers\Api\SiteController;
use App\Http\Controllers\Api\TiXianController;
use App\Http\Controllers\Api\HealthyReportController;
use App\Http\Controllers\Api\VaccineRecordController;
use App\Http\Controllers\Api\RiderSettingController;
use App\Http\Controllers\Api\WukongIMController;
use App\Http\Controllers\Api\RouteController;
use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('v1')->name('api.v1.')->group(function () {

    // 短信验证码
    Route::post('verificationCodes', [VerificationCodesController::class, 'store'])->middleware(\App\Http\Middleware\VerifySignature::class)->name('verificationCodes.store');

    // 用户注册
    Route::post('users', [UsersController::class, 'store'])->name('users.store');

    // 小程序手机注册
    Route::post('users/mini_program', [UsersController::class, 'miniProgramStore'])->name('users.mini_program_store');
    // 第三方登录
    Route::post('socials/{social_type}/authorizations', [AuthorizationsController::class, 'socialStore'])->name('socials.authorizations.store');

    // 绑定手机号
    Route::post('bind_phone', [AuthorizationsController::class, 'bindPhone']);

    // 手机号验证码登录
    Route::post('authorizations', [AuthorizationsController::class, 'store'])->name('authorizations.store');

    // 忘记密码
    Route::post('forgetPassword', [AuthorizationsController::class, 'forgetPassword'])->name('authorizations.forget');

    // 修改手机号校验
    Route::post('valid_change_phone', [UsersController::class, 'validChangePhone'])->name('user.valid_change_phone');

    // 用户数据同步
    Route::post('register_from_sync', [UsersController::class, 'sync'])->name('user.sync');

    // 游客可访问的接口
    Route::post('smartAddress', [AddressController::class, 'smart']);
    // 城市列表
    Route::get('cities', [RegionController::class, 'index']);

    Route::get('index', [HomeController::class, 'indexForSQ']);
    Route::get('config', [CommonController::class, 'config']);
    Route::get('common', [CommonController::class, 'common']);
    Route::get('check_version', [CommonController::class, 'checkVersion']);
    Route::get('phone_protect', [PhoneProtectController::class, 'index']);

    Route::get('pt_index', [HomeController::class, 'indexForPT']);
    Route::get('get_rider_count', [HomeController::class, 'getRiderCount']);

    Route::apiResource("communities", CommunityController::class)->only(['index']); // 社区
    Route::apiResource("shops", ShopController::class)->only(['index', 'show']); // 店铺
    Route::apiResource("shop_cats", ShopSpuCatController::class)->only(['index']); // 商品类目
    Route::apiResource("shop_spus", ShopSpuController::class)->only(['index']); // 商品

    Route::get('coupons', [CouponController::class, "index"]); //优惠券
    Route::get("categories", [CategoryController::class, "index"]); //类目
    Route::get('appoint_time', [CommonController::class, 'appointTime']); //预约时间
    Route::get("advertisements", [AdvertisementController::class, "index"]); //广告位
    Route::get('notifies', [NotifyController::class, "index"]); //通知

    // 环信用户信息获取
    Route::post('easemob/user/get', [EasemobController::class, 'fetchUserInfo']);

    // 骑手端未登录接口
    Route::get('options', [CommonController::class, 'options']);
    Route::get('sites/{id}', [SiteController::class, 'show']);
    Route::post('save_site_region', [SiteController::class, 'saveSiteRegion']);
    // 地址搜索
    Route::get('address_search', [AddressController::class, 'search'])->name('address.search');

    // 地址逆向
    Route::get('address_coder', [AddressController::class, 'geoCoder'])->name('address.coder');

    // 登录后可以访问的接口
    Route::middleware(['auth:api', 'check'])->group(function () {
        // 当前登录用户信息
        Route::get('user', [UsersController::class, 'me'])->name('user.show');

        // 编辑登录用户信息
        Route::patch('user', [UsersController::class, 'update'])->name('user.update');

        // 获取环信用户token
        Route::get('easemob/token', [EasemobController::class, 'getToken']);

        // 修改手机号
        Route::post('change_phone', [UsersController::class, 'changePhone'])->name('user.change_phone');
        // 注销用户
        Route::post('logout', [UsersController::class, 'logout'])->name('user.logout');

        // 上传图片
        Route::post('images', [ImagesController::class, 'store'])->name('images.store');

        // 用户认证
        Route::post('id_card_auth', [UsersController::class, 'idCardAuth']);

        // 我的地址
        Route::get('address', [AddressController::class, 'index'])->name('address.index');
        Route::post('address', [AddressController::class, 'store'])->name('address.store');
        Route::patch('address/{id}', [AddressController::class, 'update'])->name('address.update');
        Route::delete('address/{id}', [AddressController::class, 'destroy'])->name('address.destroy');
        Route::get("common_address", [AddressController::class, 'commonAddress']);

        // 购物车
        Route::get('cart', [CartController::class, 'index'])->name('cart.index');
        Route::post('cart/change', [CartController::class, 'change'])->name('cart.change');
        Route::post('cart/del', [CartController::class, 'delete'])->name('cart.delete');

        Route::apiResource("orders", OrderController::class)->only(['index', 'show', 'store']); // 订单
        Route::post('orders/pre', [OrderController::class, 'pre'])->name('orders.pre');
        Route::post('orders/cancel', [OrderController::class, 'cancel'])->name('orders.cancel');
        Route::post('orders/refund', [OrderController::class, 'refund'])->name('orders.refund');

        // 用户钱包
        Route::get('user_account', [UserAccountController::class, 'index'])->name('user.account.index');
        Route::get('user_account_flow', [UserAccountFlowController::class, 'index'])->name('user.account_flow.index');
        Route::get('user_account_flow/{id}', [UserAccountFlowController::class, 'show'])->name('user.account_flow.show');

        // 充值
        Route::post('recharge_order', [RechargeOrderController::class, 'store'])->name('recharge_order.store');

        // 支付(统一支付接口 支持钱包充值，跑腿订单，社区食堂订单支付)
        Route::post('pay', [PayController::class, 'pay']);

        // 意见反馈
        Route::get('suggestion', [SuggestionController::class, 'index'])->name('suggestion.index');
        Route::post('suggestion', [SuggestionController::class, 'store'])->name('suggestion.store');

        // 用户邀请奖励活动
        Route::get('user_activity', [ActivityController::class, 'userActivity'])->name('activity.user.index');
        // 骑手邀请奖励活动
        Route::get('rider_activity', [ActivityController::class, 'riderActivity'])->name('activity.rider.index');


        // ----------------------------------------------- 骑手端 ------------------------------------------- //
        // 身份证识别
        Route::post('id_card_ocr', [CommonController::class, 'idCardOcr']);
        // 驾驶证识别
        Route::post('driver_ocr', [CommonController::class, 'driverCardOcr']);
        // 行驶证识别
        Route::post('driving_ocr', [CommonController::class, 'drivingCardOcr']);
        // 身份信息确认
        Route::post('complete_idcard', [UsersController::class, 'idCardStore']);
        // 人像对比
        Route::post('face_compare', [UsersController::class, 'faceCompare']);

        // 提交骑手申请
        Route::post('rider_applies', [RiderApplyController::class, 'store']);
        // 健康申报
        Route::post('healthy_reports', [HealthyReportController::class, 'store']);
        // 疫苗记录上报
        Route::post('vaccine_record', [VaccineRecordController::class, 'store']);
        // 接单设置
        Route::get('rider_settings', [RiderSettingController::class, 'index']);
        // 接单设置更新
        Route::post('rider_settings', [RiderSettingController::class, 'update']);

        // 任务列表
        Route::get('task', [TaskController::class, 'index'])->name('task.index')->middleware('checkRider');

        // 订单列表
        Route::get('task_orders', [TaskController::class, 'taskOrder'])->name('task.order');

        // 进行中的任务
        Route::get('running_task', [TaskController::class, 'running'])->name('task.running');

        // 任务详情
        Route::get('task/detail', [TaskController::class, 'detail'])->name('task.detail');

        // 接单
        Route::post('do_task', [TaskController::class, 'doTask'])->name('task.do')->middleware('checkRider');

        // 校验取件码，收货吗
        Route::post('check_code', [TaskController::class, 'checkCode'])->name('task.check_code');

        // 到达取货点
        Route::post('arrive', [TaskController::class, 'arrive']);

        // 确认取件
        Route::post('pickup', [TaskController::class, 'pickup']);

        // 确认送达
        Route::post('receive', [TaskController::class, 'receive']);

        // 接受派单
        Route::post('agree_dispatch_order', [TaskController::class, 'agreeDispatch']);

        // 拒绝派单
        Route::post('reject_dispatch_order', [TaskController::class, 'rejectDispatch']);

        // 转单列表
        Route::get('trans_order_list', [TaskController::class, 'transOrderList']);

        // 转单剩余次数查询
        Route::get('trans_times_query', [TaskController::class, 'queryTransTimes']);

        // 发起转单
        Route::post('trans_order', [TaskController::class, 'trans']);

        // 更新推送RID
        Route::post('upload_rid', [UsersController::class, 'RidUpload']);

        // 更新骑手状态
        Route::post('rider_status', [RiderController::class, 'updateStatus']);
        // 骑手信息
        Route::get('rider', [RiderController::class, 'me'])->name('rider.me');
        // 详细资料
        Route::get('rider_profile', [RiderController::class, 'profile'])->name('rider.profile');
        Route::post('rider_profile', [RiderController::class, 'updateProfile'])->name('rider.profile.update');
        // 我的账户
        Route::get('rider_account', [RiderAccountController::class, 'account'])->name('rider.account');
        Route::get('rider_account_flow', [RiderAccountFlowController::class, 'index'])->name('rider.account_flow.index');
        Route::get('rider_account_flow/{id}', [RiderAccountFlowController::class, 'show'])->name('rider.account_flow.show');
        // 提现
        Route::post('rider_tx', [TiXianController::class, 'store'])->name('rider.tx.store');
        // 保证金
        Route::get('earnest_money', [RiderAccountController::class, 'earnestAccount']);
        Route::post('earnest_recharge_order', [RechargeOrderController::class, 'earnestRecharge'])->name('earnest_recharge_order.store');
        Route::post('earnest_transfer', [RechargeOrderController::class, 'earnestTransfer']);

        // 经纬度上报
        Route::post("location", [LocationController::class, 'store'])->name('location.store');
        // 健康证
        Route::get('health_cert', [HealthCertController::class, 'my']);
        Route::post('health_cert', [HealthCertController::class, 'store']);
        // 交通工具
        Route::get('transport/{type}', [TransportController::class, 'show']);
        Route::post('transport', [TransportController::class, 'store']);
        Route::get('driver_licenses', [TransportController::class, 'driverLicenses']);
        Route::get('driving_licenses', [TransportController::class, 'drivingLicense']);
        Route::post('upload_driver_licenses', [TransportController::class, 'driverLicensesStore'])->name('driver.licenses.store');
        Route::post('upload_driving_licenses', [TransportController::class, 'drivingLicensesStore'])->name('driving.licenses.store');

        //企业合作
        Route::get('cooperate', [CooperateController::class, 'info'])->name('cooperate.info'); //获取企业合作信息
        Route::post('cooperate', [CooperateController::class, 'store'])->name('cooperate.store'); //企业合作填写

        Route::apiResource('user_coupons', UserCouponController::class)->only(['index', 'store']); //用户优惠券

        Route::get('share_info', [UsersController::class, 'share'])->name('users.share'); //用户分享

        //帮我*订单
        Route::apiResource("errand_orders", O2oErrandOrderController::class)->only(['index', 'show', 'store']); //列表、详情、预下单
        Route::post('errand_orders/pre', [O2oErrandOrderController::class, 'pre'])->name('errand_orders.pre'); //费用明细计算
        Route::post('errand_orders/status', [O2oErrandOrderController::class, 'status'])->name('errand_orders.status'); //刷状态
        Route::post('errand_orders/cancel', [O2oErrandOrderController::class, 'cancel'])->name('errand_orders.cancel'); //待支付取消订单
        Route::post('errand_orders/refund', [O2oErrandOrderController::class, 'refund'])->name('errand_orders.refund'); //已支付取消订单
        Route::post('errand_orders/gratuity', [O2oErrandOrderController::class, 'gratuity'])->name('errand_orders.gratuity'); //加小费
        Route::post('errand_orders/remind', [O2oErrandOrderController::class, 'remind'])->name('errand_orders.remind'); //催单
        Route::post('errand_orders/validate_code', [O2oErrandOrderController::class, 'validateCode'])->name('errand_orders.validate_code'); //验证取货码/收货码
        Route::post('errand_orders/reward', [O2oErrandOrderController::class, 'reward'])->name('errand_orders.reward'); //打赏
        Route::post('errand_orders/evaluate', [O2oErrandOrderController::class, 'evaluate'])->name('errand_orders.evaluate'); //评论

        // 从图片提取地址信息并创建新地址
        Route::post('extract_address', [AddressController::class, 'extractAndCreate'])->name('address.extract');

        // 路径规划接口 - 这里添加了路径规划相关的路由
        Route::post('route/optimize', [RouteController::class, 'optimize']);
        Route::post('route/constrained', [RouteController::class, 'constrained']);

        // 骑手路径规划接口 - 登录后可访问
        Route::post('rider/route', [RouteController::class, 'riderRoute']);
        // 高德地图专用路径规划接口
        Route::post('rider/path_planning', [RouteController::class, 'riderRoute']);

        // 骑手多路径规划相关接口
        Route::post('rider/route/multi_task', [RouteController::class, 'multiTaskRouteV2']);
        Route::get('rider/route/detail/{id}', [RouteController::class, 'routeDetail']);
        Route::post('rider/route/save', [RouteController::class, 'saveRoute']);
        Route::post('rider/route/navigate', [RouteController::class, 'startNavigation']);
        Route::get('rider/route/savings', [RouteController::class, 'routeSavings']);
    });
    Route::prefix('open')->name('api.open.v1')->group(function () {
        Route::post('auth', [\App\Http\Controllers\Api\Open\AuthorizationsController::class, 'auth'])->name('auth');
        Route::middleware('auth:api')->group(function () {
            Route::post('mall_errand_order', [\App\Http\Controllers\Api\Open\OrderController::class, 'store']);
            Route::post('mall_errand_order/cancel', [\App\Http\Controllers\Api\Open\OrderController::class, 'cancel']);
        });
    });

    // 省市区数据接口
    Route::prefix('regions')->group(function () {
        Route::get('provinces', [RegionController::class, 'provinces']);
        Route::get('provinces/{provinceCode}/cities', [RegionController::class, 'cities']);
        Route::get('provinces/{provinceCode}/cities/{cityCode}/districts', [RegionController::class, 'districts']);
    });

    // 悟空IM相关接口
    Route::get('/wukongim/token', [WukongIMController::class, 'getToken']);

    // 路径规划接口 - 这里添加了路径规划相关的路由
    Route::post('route/optimize', [RouteController::class, 'optimize']);
    Route::post('route/constrained', [RouteController::class, 'constrained']);
});

Route::prefix('admin/v1')->name('api.admin.v1.')->group(function () {
    Route::get('riders', [])->name('riders.index');
    Route::post('dispatch_order', [TaskController::class, 'dispatchOrder']);
});

Route::prefix('myt')->group(function () {
    Route::get('auth/callback', [\App\Http\Controllers\Api\MaiYaTianController::class, 'authCallback']);
    Route::post('callback/{command}', [\App\Http\Controllers\Api\MaiYaTianController::class, 'callback']);
    Route::post('merchant/callback', [\App\Http\Controllers\Api\MaiYaTianController::class, 'merchantCallback'])->name('api.merchant.callback');
});

Route::prefix('haibo')->middleware('verify.haibo.signature')->group(function () {
    Route::post('store', [\App\Http\Controllers\Api\HaiboController::class, 'createOrUpdateStore'])->name('api.haibo.store');
    Route::post('valuating', [\App\Http\Controllers\Api\HaiboController::class, 'valuating'])->name('api.haibo.valuating');
    Route::post('rider-location', [\App\Http\Controllers\Api\HaiboController::class, 'riderLocation'])->name('api.haibo.rider-location');
    Route::post('send', [\App\Http\Controllers\Api\HaiboController::class, 'send'])->name('api.haibo.send');
    Route::post('cancel', [\App\Http\Controllers\Api\HaiboController::class, 'cancel'])->name('api.haibo.cancel');
    Route::post('order-detail', [\App\Http\Controllers\Api\HaiboController::class, 'orderDetail'])->name('api.haibo.order-detail');
    Route::post('add-tip', [\App\Http\Controllers\Api\HaiboController::class, 'addTip'])->name('api.haibo.add-tip');
});

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
