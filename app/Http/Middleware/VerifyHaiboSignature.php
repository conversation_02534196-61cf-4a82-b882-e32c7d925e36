<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 海博API签名验证中间件
 *
 * 签名协议：
 * 1. 将所有系统参数及业务参数（其中sign，byte[]及值为空的参数除外）按照参数名的字典顺序排序
 * 2. 将参数以参数1值1参数2值2...的顺序拼接，例如a=&c=3&b=1，变为b1c3，参数使用utf-8编码
 * 3. 按照secret + 排序后的参数的顺序进行连接，得到加密前的字符串
 * 4. 对加密前的字符串进行sha1加密并转为小写字符串，得到签名
 * 5. 将得到的签名赋给sign作为请求的参数
 */
class VerifyHaiboSignature
{
    /**
     * 处理传入的请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            // 记录验签请求
            Log::channel('haibo')->info('海博API验签开始', [
                'url' => $request->url(),
                'method' => $request->method(),
                'params' => $request->all()
            ]);

            // 验证必需的系统参数
            $validation = $this->validateRequiredParams($request);
            if ($validation !== true) {
                return $validation;
            }

            // 验证签名
            $signatureValidation = $this->validateSignature($request);
            if ($signatureValidation !== true) {
                return $signatureValidation;
            }

            // 验证时间戳（可选：防重放攻击）
            $timestampValidation = $this->validateTimestamp($request);
            if ($timestampValidation !== true) {
                return $timestampValidation;
            }

            Log::channel('haibo')->info('海博API验签成功');

            return $next($request);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博API验签异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return $this->errorResponse('系统错误');
        }
    }

    /**
     * 验证必需参数
     *
     * @param Request $request
     * @return mixed
     */
    private function validateRequiredParams(Request $request)
    {
        $requiredParams = ['developerId', 'timestamp', 'version', 'sign'];

        foreach ($requiredParams as $param) {
            if (!$request->has($param) || $request->input($param) === null || $request->input($param) === '') {
                Log::channel('haibo')->warning('海博API验签失败：缺少必需参数', [
                    'missing_param' => $param,
                    'request_data' => $request->all()
                ]);

                return $this->errorResponse("缺少必需参数: {$param}");
            }
        }

        // 验证developerId
        $developerId = $request->input('developerId');
        $configDeveloperId = config('haibo.developer_id');

        if ($developerId !== $configDeveloperId) {
            Log::channel('haibo')->warning('海博API验签失败：developerId无效', [
                'provided_developer_id' => $developerId,
                'expected_developer_id' => $configDeveloperId
            ]);

            return $this->errorResponse('developerId无效');
        }

        // 验证version
        $version = $request->input('version');
        if ($version !== '1.0') {
            Log::channel('haibo')->warning('海博API验签失败：不支持的API版本', [
                'provided_version' => $version
            ]);

            return $this->errorResponse('不支持的API版本');
        }

        return true;
    }

    /**
     * 验证签名
     *
     * @param Request $request
     * @return mixed
     */
    private function validateSignature(Request $request)
    {
        $providedSign = $request->input('sign');
        $expectedSign = $this->generateSignature($request);

        if ($providedSign !== $expectedSign) {
            Log::channel('haibo')->warning('海博API验签失败：签名不匹配', [
                'provided_sign' => $providedSign,
                'expected_sign' => $expectedSign,
                'request_data' => $request->all()
            ]);

            return $this->errorResponse('签名验证失败');
        }

        return true;
    }

    /**
     * 验证时间戳（防重放攻击）
     *
     * @param Request $request
     * @return mixed
     */
    private function validateTimestamp(Request $request)
    {
        $timestamp = $request->input('timestamp');
        $currentTime = time();

        // 允许5分钟的时间差
        $allowedTimeDiff = 300;

        if (abs($currentTime - $timestamp) > $allowedTimeDiff) {
            Log::channel('haibo')->warning('海博API验签失败：时间戳过期', [
                'provided_timestamp' => $timestamp,
                'current_timestamp' => $currentTime,
                'time_diff' => abs($currentTime - $timestamp)
            ]);

            return $this->errorResponse('请求时间戳过期');
        }

        return true;
    }

    /**
     * 生成签名 - 按照海博签名协议
     *
     * @param Request $request
     * @return string
     */
    private function generateSignature(Request $request): string
    {
        // 获取原始请求数据，避免Laravel自动trim空格
        $rawInput = $request->getContent();

        // 根据Content-Type解析原始数据
        $contentType = $request->header('Content-Type', '');

        if (strpos($contentType, 'application/json') !== false) {
            // JSON格式
            $params = json_decode($rawInput, true) ?: [];
        } else {
            // 表单格式，手动解析避免自动trim
            parse_str($rawInput, $params);
        }

        // 如果解析失败，回退到request->all()
        if (empty($params)) {
            $params = $request->all();
        }

        $secret = config('haibo.secret');

        // 1. 将所有系统参数及业务参数（其中sign，byte[]及值为空的参数除外）按照参数名的字典顺序排序
        $filteredParams = [];
        foreach ($params as $key => $value) {
            // 排除 sign 参数和空值参数
            if ($key === 'sign' || $value === null || $value === '') {
                continue;
            }

            // 数组和对象转为JSON字符串
            if (is_array($value) || is_object($value)) {
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            }

            // 布尔值转换
            if (is_bool($value)) {
                $value = $value ? 'true' : 'false';
            }

            $filteredParams[$key] = $value;
        }

        // 按参数名字典顺序排序
        ksort($filteredParams);

        // 2. 将参数以参数1值1参数2值2...的顺序拼接
        $signString = '';
        foreach ($filteredParams as $key => $value) {
            $signString .= $key . $value;
        }

        // 3. 按照secret + 排序后的参数的顺序进行连接，得到加密前的字符串
        $signString = $secret . $signString;

        // 调试日志：记录签名计算过程
        Log::channel('haibo')->debug('签名计算过程', [
            'filtered_params' => $filteredParams,
            'param_string' => substr($signString, strlen($secret)), // 去掉secret的参数部分
            'full_sign_string' => $signString,
            'calculated_sign' => strtolower(sha1($signString))
        ]);

        // 4. 对加密前的字符串进行sha1加密并转为小写字符串，得到签名
        return strtolower(sha1($signString));
    }

    /**
     * 返回错误响应
     *
     * @param string $message
     * @return \Illuminate\Http\JsonResponse
     */
    private function errorResponse(string $message)
    {
        return response()->json([
            'code' => 1, // 海博规范：非0表示失败
            'message' => $message,
            'data' => null
        ], 200);
    }
}
